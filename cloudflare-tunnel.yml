# Cloudflare Tunnel Configuration for ATMA API Gateway
# This file defines how Cloudflare Tunnel routes traffic to your API Gateway

# Tunnel configuration
tunnel: your-tunnel-id-here
credentials-file: /etc/cloudflared/credentials.json

# Ingress rules - routes traffic from your domain to the API Gateway
ingress:
  # Route all traffic to API Gateway
  - hostname: your-domain.com
    service: http://api-gateway:3000
    originRequest:
      # Connection settings
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      httpHostHeader: your-domain.com
      # Disable HTTP/2 for better compatibility if needed
      # disableChunkedEncoding: true
      
  # Route API subdomain (optional)
  - hostname: api.your-domain.com
    service: http://api-gateway:3000
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      httpHostHeader: api.your-domain.com

  # WebSocket support for notifications
  - hostname: ws.your-domain.com
    service: http://api-gateway:3000
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      httpHostHeader: ws.your-domain.com

  # Catch-all rule (required)
  - service: http_status:404

# Logging configuration
loglevel: info

# Metrics server (optional)
metrics: 0.0.0.0:8080

# Auto-update settings
no-autoupdate: true

# Protocol settings
protocol: auto
