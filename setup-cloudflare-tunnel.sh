#!/bin/bash

# ATMA Cloudflare Tunnel Setup Script
# This script helps you setup Cloudflare Zero Trust Tunnel for ATMA API Gateway

set -e

echo "🚀 ATMA Cloudflare Tunnel Setup"
echo "================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.cloudflare .env
    echo "✅ .env file created. Please edit it with your tunnel token."
    echo ""
    echo "To get your tunnel token:"
    echo "1. Go to https://one.dash.cloudflare.com/"
    echo "2. Navigate to Access > Tunnels"
    echo "3. Create a new tunnel or select existing one"
    echo "4. Copy the tunnel token and paste it in .env file"
    echo ""
    read -p "Press Enter after you've updated the .env file with your tunnel token..."
fi

# Validate tunnel token
source .env
if [ "$CLOUDFLARE_TUNNEL_TOKEN" = "your_actual_tunnel_token_here" ] || [ -z "$CLOUDFLARE_TUNNEL_TOKEN" ]; then
    echo "❌ Please set your actual tunnel token in .env file"
    echo "Current token: $CLOUDFLARE_TUNNEL_TOKEN"
    exit 1
fi

echo "✅ Tunnel token configured"

# Check if API Gateway is running
echo "🔍 Checking API Gateway status..."
if docker-compose ps api-gateway | grep -q "Up"; then
    echo "✅ API Gateway is running"
else
    echo "⚠️  API Gateway is not running. Starting all services..."
    docker-compose up -d
    echo "⏳ Waiting for services to be ready..."
    sleep 30
fi

# Start Cloudflare Tunnel
echo "🌐 Starting Cloudflare Tunnel..."
docker-compose up -d cloudflare-tunnel

# Wait for tunnel to connect
echo "⏳ Waiting for tunnel to connect..."
sleep 10

# Check tunnel status
echo "🔍 Checking tunnel status..."
if docker-compose logs cloudflare-tunnel | grep -q "Connection established"; then
    echo "✅ Tunnel connected successfully!"
elif docker-compose logs cloudflare-tunnel | grep -q "Registered tunnel connection"; then
    echo "✅ Tunnel registered successfully!"
else
    echo "⚠️  Tunnel status unclear. Checking logs..."
    docker-compose logs --tail=20 cloudflare-tunnel
fi

# Test local API Gateway
echo "🧪 Testing local API Gateway..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Local API Gateway is responding"
else
    echo "❌ Local API Gateway is not responding"
    echo "Please check API Gateway logs:"
    echo "docker-compose logs api-gateway"
    exit 1
fi

echo ""
echo "🎉 Setup completed!"
echo ""
echo "Next steps:"
echo "1. Configure public hostname in Cloudflare Dashboard:"
echo "   - Go to your tunnel configuration"
echo "   - Add public hostname pointing to api-gateway:3000"
echo "2. Wait for DNS propagation (5-10 minutes)"
echo "3. Test your public API endpoint"
echo ""
echo "Useful commands:"
echo "  Check tunnel logs: docker-compose logs cloudflare-tunnel"
echo "  Check API logs:    docker-compose logs api-gateway"
echo "  Restart tunnel:    docker-compose restart cloudflare-tunnel"
echo "  Stop tunnel:       docker-compose stop cloudflare-tunnel"
echo ""
echo "📖 For detailed setup instructions, see CLOUDFLARE_TUNNEL_SETUP.md"
