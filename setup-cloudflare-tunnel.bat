@echo off
setlocal enabledelayedexpansion

echo 🚀 ATMA Cloudflare Tunnel Setup
echo ================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose not found. Please install docker-compose first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo 📝 Creating .env file from template...
    copy .env.cloudflare .env >nul
    echo ✅ .env file created. Please edit it with your tunnel token.
    echo.
    echo To get your tunnel token:
    echo 1. Go to https://one.dash.cloudflare.com/
    echo 2. Navigate to Access ^> Tunnels
    echo 3. Create a new tunnel or select existing one
    echo 4. Copy the tunnel token and paste it in .env file
    echo.
    pause
)

REM Check if tunnel token is configured
findstr /C:"CLOUDFLARE_TUNNEL_TOKEN=your_actual_tunnel_token_here" .env >nul
if not errorlevel 1 (
    echo ❌ Please set your actual tunnel token in .env file
    echo Open .env file and replace 'your_actual_tunnel_token_here' with your actual token
    pause
    exit /b 1
)

findstr /C:"CLOUDFLARE_TUNNEL_TOKEN=" .env | findstr /V /C:"your_actual_tunnel_token_here" >nul
if errorlevel 1 (
    echo ❌ Please set your actual tunnel token in .env file
    pause
    exit /b 1
)

echo ✅ Tunnel token configured

REM Check if API Gateway is running
echo 🔍 Checking API Gateway status...
docker-compose ps api-gateway | findstr "Up" >nul
if errorlevel 1 (
    echo ⚠️  API Gateway is not running. Starting all services...
    docker-compose up -d
    echo ⏳ Waiting for services to be ready...
    timeout /t 30 /nobreak >nul
) else (
    echo ✅ API Gateway is running
)

REM Start Cloudflare Tunnel
echo 🌐 Starting Cloudflare Tunnel...
docker-compose up -d cloudflare-tunnel

REM Wait for tunnel to connect
echo ⏳ Waiting for tunnel to connect...
timeout /t 10 /nobreak >nul

REM Check tunnel status
echo 🔍 Checking tunnel status...
docker-compose logs cloudflare-tunnel | findstr "Connection established" >nul
if not errorlevel 1 (
    echo ✅ Tunnel connected successfully!
    goto :test_api
)

docker-compose logs cloudflare-tunnel | findstr "Registered tunnel connection" >nul
if not errorlevel 1 (
    echo ✅ Tunnel registered successfully!
    goto :test_api
)

echo ⚠️  Tunnel status unclear. Checking logs...
docker-compose logs --tail=20 cloudflare-tunnel

:test_api
REM Test local API Gateway
echo 🧪 Testing local API Gateway...
curl -s http://localhost:3000/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ Local API Gateway is responding
) else (
    echo ❌ Local API Gateway is not responding
    echo Please check API Gateway logs:
    echo docker-compose logs api-gateway
    pause
    exit /b 1
)

echo.
echo 🎉 Setup completed!
echo.
echo Next steps:
echo 1. Configure public hostname in Cloudflare Dashboard:
echo    - Go to your tunnel configuration
echo    - Add public hostname pointing to api-gateway:3000
echo 2. Wait for DNS propagation (5-10 minutes)
echo 3. Test your public API endpoint
echo.
echo Useful commands:
echo   Check tunnel logs: docker-compose logs cloudflare-tunnel
echo   Check API logs:    docker-compose logs api-gateway
echo   Restart tunnel:    docker-compose restart cloudflare-tunnel
echo   Stop tunnel:       docker-compose stop cloudflare-tunnel
echo.
echo 📖 For detailed setup instructions, see CLOUDFLARE_TUNNEL_SETUP.md
echo.
pause
