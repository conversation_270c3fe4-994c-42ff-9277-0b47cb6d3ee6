# Cloudflare Zero Trust Tunnel Setup untuk ATMA API Gateway

Panduan ini akan membantu Anda mengekspos ATMA API Gateway ke public menggunakan Cloudflare Zero Trust Tunnel.

## Prerequisites

1. Akun Cloudflare dengan domain yang sudah dikonfigurasi
2. <PERSON><PERSON><PERSON> ke Cloudflare Zero Trust Dashboard
3. Docker dan Docker Compose terinstall

## Langkah 1: Setup Cloudflare Tunnel

### 1.1 Buat Tunnel di Cloudflare Dashboard

1. <PERSON>gin ke [Cloudflare Zero Trust Dashboard](https://one.dash.cloudflare.com/)
2. Navigasi ke **Access** > **Tunnels**
3. Klik **Create a tunnel**
4. Pilih **Cloudflared** sebagai connector type
5. Berikan nama untuk tunnel Anda (contoh: `atma-api-gateway`)
6. Klik **Save tunnel**

### 1.2 Dapatkan Tunnel Token

1. Setelah tunnel dibuat, Anda akan melihat halaman konfigurasi
2. Pilih **Docker** sebagai environment
3. Copy **tunnel token** yang ditampilkan (dimulai dengan `eyJ...`)
4. Simpan token ini untuk langkah selanjutnya

### 1.3 Konfigurasi Public Hostname

1. Di halaman tunnel configuration, scroll ke bagian **Public Hostnames**
2. Klik **Add a public hostname**
3. Konfigurasi sebagai berikut:
   - **Subdomain**: `api` (atau kosong untuk root domain)
   - **Domain**: pilih domain Anda
   - **Path**: kosong
   - **Service Type**: `HTTP`
   - **URL**: `api-gateway:3000`
4. Klik **Save hostname**

## Langkah 2: Konfigurasi Environment

### 2.1 Setup Environment Variables

1. Copy file environment template:
   ```bash
   cp .env.cloudflare .env
   ```

2. Edit file `.env` dan ganti `your_actual_tunnel_token_here` dengan tunnel token yang Anda dapatkan:
   ```bash
   CLOUDFLARE_TUNNEL_TOKEN=eyJhIjoiYWJjZGVmZ2hpams...
   ```

### 2.2 Update docker-compose.yml (Opsional)

Jika Anda ingin menggunakan file konfigurasi tunnel alih-alih token, edit `cloudflare-tunnel.yml`:

1. Ganti `your-tunnel-id-here` dengan Tunnel ID Anda
2. Ganti `your-domain.com` dengan domain Anda yang sebenarnya
3. Uncomment dan modifikasi service cloudflare-tunnel di docker-compose.yml untuk menggunakan config file

## Langkah 3: Deploy

### 3.1 Start Services

```bash
# Start semua services termasuk tunnel
docker-compose up -d

# Atau start hanya tunnel jika services lain sudah berjalan
docker-compose up -d cloudflare-tunnel
```

### 3.2 Verifikasi Tunnel

```bash
# Check status tunnel
docker-compose logs cloudflare-tunnel

# Check status API Gateway
curl http://localhost:3000/health
```

## Langkah 4: Testing

### 4.1 Test Public Access

1. Tunggu beberapa menit untuk DNS propagation
2. Test akses public ke API:
   ```bash
   curl https://api.your-domain.com/health
   ```

3. Test endpoint lainnya:
   ```bash
   # Health check
   curl https://api.your-domain.com/api/health
   
   # API Gateway info
   curl https://api.your-domain.com/
   ```

### 4.2 Test WebSocket (untuk notifications)

Jika Anda mengkonfigurasi subdomain untuk WebSocket:
```javascript
// Test WebSocket connection
const socket = io('https://ws.your-domain.com');
```

## Konfigurasi Keamanan Tambahan

### Rate Limiting
API Gateway sudah memiliki rate limiting built-in:
- General: 5000 requests per 15 menit
- Auth endpoints: 2500 requests per 15 menit
- Assessment: 1000 submissions per jam

### CORS
CORS sudah dikonfigurasi untuk menerima semua origins (`*`). Untuk production, pertimbangkan untuk membatasi origins:

```yaml
environment:
  ALLOWED_ORIGINS: "https://your-frontend-domain.com,https://api.your-domain.com"
```

### Authentication
Semua protected endpoints sudah memerlukan JWT token. Public endpoints:
- `/health/*` - Health checks
- `/api/auth/register` - User registration
- `/api/auth/login` - User login

## Troubleshooting

### Tunnel tidak terhubung
```bash
# Check tunnel logs
docker-compose logs cloudflare-tunnel

# Restart tunnel
docker-compose restart cloudflare-tunnel
```

### Domain tidak dapat diakses
1. Pastikan DNS sudah propagated (tunggu 5-10 menit)
2. Check konfigurasi hostname di Cloudflare Dashboard
3. Pastikan API Gateway berjalan: `curl http://localhost:3000/health`

### Error 502 Bad Gateway
1. Pastikan API Gateway healthy
2. Check network connectivity antara tunnel dan API Gateway
3. Verify service URLs di konfigurasi tunnel

## Monitoring

### Cloudflare Analytics
Monitor traffic dan performance di Cloudflare Dashboard > Analytics

### Application Logs
```bash
# API Gateway logs
docker-compose logs api-gateway

# Tunnel logs
docker-compose logs cloudflare-tunnel

# All services logs
docker-compose logs
```

## Security Best Practices

1. **Gunakan HTTPS Only**: Cloudflare Tunnel otomatis menggunakan HTTPS
2. **Monitor Access Logs**: Review logs secara berkala
3. **Update Dependencies**: Keep cloudflared image updated
4. **Restrict Origins**: Limit CORS origins untuk production
5. **Use Strong JWT Secrets**: Pastikan JWT_SECRET kuat dan unik

## Backup dan Recovery

### Backup Tunnel Configuration
Simpan tunnel token dan konfigurasi di tempat yang aman.

### Recovery
Jika tunnel bermasalah:
1. Recreate tunnel di Cloudflare Dashboard
2. Update tunnel token di environment
3. Restart services

---

Untuk pertanyaan atau masalah, check logs dan dokumentasi Cloudflare Zero Trust.
